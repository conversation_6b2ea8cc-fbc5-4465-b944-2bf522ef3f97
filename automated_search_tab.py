"""
Interface da aba de busca automatizada para a versão desktop do PROSPECTO.
"""
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
                            QLineEdit, QProgressBar, QFrame, QScrollArea, QRadioButton,
                            QButtonGroup, QFileDialog, QMessageBox, QSpacerItem, QSizePolicy,
                            QDialog, QListWidget, QListWidgetItem, QCheckBox)
from PyQt6.QtCore import Qt, QSize
from PyQt6.QtGui import QIcon, QPixmap
import os
import config_manager

from automated_search_desktop import AutomatedSearchThread
from desktop_theme import (
    PRIMARY_COLOR, SECONDARY_COLOR, ACCENT_COLOR, BACK<PERSON>OUND_COLOR, CARD_COLOR,
    TEXT_PRIMARY, TEXT_SECONDARY, SUCCESS_COLOR, ERROR_COLOR, WARNING_COLOR, INFO_COLOR,
    style_heading_label, style_subheading_label, style_body_label, style_caption_label,
    create_card_frame, style_primary_button, style_secondary_button, style_accent_button
)



class AutomatedSearchTab(QWidget):
    """Aba de busca automatizada no Google Maps."""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.search_queries = []
        self.query_fields = []
        self.search_thread = None

        # Carregar configurações
        self.config = config_manager.load_config()

        self.initUI()

    def initUI(self):
        """Inicializa a interface da aba."""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)

        # Título da aba
        title_label = QLabel("Busca Automatizada no Google Maps")
        style_heading_label(title_label)
        layout.addWidget(title_label)

        # Descrição
        description = QLabel("Execute buscas automatizadas com as configurações definidas no SETUP.")
        style_body_label(description)
        layout.addWidget(description)

        # Botão SETUP
        setup_button = QPushButton("SETUP")
        setup_button.setIcon(QIcon.fromTheme("preferences-system"))
        style_primary_button(setup_button)
        setup_button.clicked.connect(self.open_setup)
        layout.addWidget(setup_button)

        # Status das configurações
        self.config_status = QLabel("Configurações: Não definidas")
        style_body_label(self.config_status)
        layout.addWidget(self.config_status)

        # Atualizar status das configurações
        self.update_config_status()

        # Card para controles de execução
        controls_card = create_card_frame()
        controls_layout = QVBoxLayout(controls_card)

        # Título do card
        controls_title = QLabel("Controles de Execução")
        style_subheading_label(controls_title)
        controls_layout.addWidget(controls_title)

        # Botões de controle
        buttons_layout = QHBoxLayout()

        self.start_button = QPushButton("Iniciar Buscas Automatizadas")
        self.start_button.setIcon(QIcon.fromTheme("media-playback-start"))
        style_primary_button(self.start_button)
        self.start_button.clicked.connect(self.start_automated_search)

        self.pause_button = QPushButton("Pausar")
        self.pause_button.setIcon(QIcon.fromTheme("media-playback-pause"))
        style_secondary_button(self.pause_button)
        self.pause_button.clicked.connect(self.pause_automated_search)
        self.pause_button.setEnabled(False)

        self.stop_button = QPushButton("Parar")
        self.stop_button.setIcon(QIcon.fromTheme("media-playback-stop"))
        style_accent_button(self.stop_button)
        self.stop_button.clicked.connect(self.stop_automated_search)
        self.stop_button.setEnabled(False)

        buttons_layout.addWidget(self.start_button)
        buttons_layout.addWidget(self.pause_button)
        buttons_layout.addWidget(self.stop_button)
        buttons_layout.addStretch()

        controls_layout.addLayout(buttons_layout)

        # Barra de progresso
        self.progress_bar = QProgressBar()
        self.progress_bar.setStyleSheet(f"""
            QProgressBar {{
                border: 1px solid #BDBDBD;
                border-radius: 4px;
                background-color: #E0E0E0;
                text-align: center;
                height: 20px;
            }}
            QProgressBar::chunk {{
                background-color: {PRIMARY_COLOR};
            }}
        """)
        controls_layout.addWidget(self.progress_bar)

        # Status
        self.status_label = QLabel("Pronto para iniciar buscas automatizadas")
        style_body_label(self.status_label)
        controls_layout.addWidget(self.status_label)

        layout.addWidget(controls_card)

        # Card para logs
        logs_card = create_card_frame()
        logs_layout = QVBoxLayout(logs_card)

        # Título do card
        logs_title = QLabel("Logs de Execução")
        style_subheading_label(logs_title)
        logs_layout.addWidget(logs_title)

        # Área de logs
        from PyQt6.QtWidgets import QTextEdit
        self.logs_area = QTextEdit()
        self.logs_area.setReadOnly(True)
        self.logs_area.setMaximumHeight(200)
        self.logs_area.setStyleSheet(f"""
            QTextEdit {{
                background-color: {CARD_COLOR};
                border: 1px solid #BDBDBD;
                border-radius: 4px;
                padding: 8px;
                font-family: 'Courier New', monospace;
                font-size: 12px;
            }}
        """)
        logs_layout.addWidget(self.logs_area)

        # Botão para limpar logs
        clear_logs_button = QPushButton("Limpar Logs")
        clear_logs_button.setIcon(QIcon.fromTheme("edit-clear"))
        style_secondary_button(clear_logs_button)
        clear_logs_button.clicked.connect(self.clear_logs)
        logs_layout.addWidget(clear_logs_button)

        layout.addWidget(logs_card)

    def open_setup(self):
        """Abre a tela de SETUP."""
        from setup_screen import SetupScreen

        # Criar a tela de setup
        setup_screen = SetupScreen(self.parent)

        # Conectar o sinal de configuração salva
        setup_screen.config_saved.connect(self.update_config_status)

        # Adicionar a tela ao stack widget
        if hasattr(self.parent, 'stacked_widget'):
            self.parent.stacked_widget.addWidget(setup_screen)
            self.parent.stacked_widget.setCurrentWidget(setup_screen)

    def update_config_status(self):
        """Atualiza o status das configurações."""
        # Recarregar configurações
        self.config = config_manager.load_config()

        queries = self.config.get("search_queries", [])
        save_dir = self.config.get("default_save_dir", "")

        if queries and save_dir:
            self.config_status.setText(
                f"Configurações: ✅ {len(queries)} consultas configuradas | "
                f"Formato: {self.config.get('default_export_format', 'excel').upper()} | "
                f"Modo: {'Headless' if self.config.get('headless_mode', True) else 'Visual'}"
            )
            self.config_status.setStyleSheet(f"color: {SUCCESS_COLOR};")
        else:
            self.config_status.setText("Configurações: ❌ Não definidas - Clique em SETUP para configurar")
            self.config_status.setStyleSheet(f"color: {ERROR_COLOR};")

    def add_log(self, message):
        """Adiciona uma mensagem aos logs."""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.logs_area.append(log_entry)

        # Auto-scroll para o final
        cursor = self.logs_area.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.logs_area.setTextCursor(cursor)

    def clear_logs(self):
        """Limpa a área de logs."""
        self.logs_area.clear()
        self.add_log("Logs limpos")

    def update_status_and_log(self, message):
        """Atualiza o status e adiciona ao log."""
        self.status_label.setText(message)
        self.add_log(message)

    def start_automated_search(self):
        """Inicia a busca automatizada."""
        # Recarregar configurações
        self.config = config_manager.load_config()

        # Validar configurações
        queries = self.config.get("search_queries", [])
        save_dir = self.config.get("default_save_dir", "")

        if not queries:
            QMessageBox.warning(self, "Erro", "Nenhuma consulta de busca configurada. Clique em SETUP para configurar.")
            return

        if not save_dir:
            QMessageBox.warning(self, "Erro", "Diretório de salvamento não configurado. Clique em SETUP para configurar.")
            return

        # Adicionar log de início
        self.add_log(f"Iniciando busca automatizada com {len(queries)} consultas")

        # Atualizar UI
        self.progress_bar.setValue(0)
        self.start_button.setEnabled(False)
        self.pause_button.setEnabled(True)
        self.stop_button.setEnabled(True)

        # Obter configurações
        file_format = self.config.get("default_export_format", "excel")
        headless_mode = self.config.get("headless_mode", True)

        # Criar e iniciar thread de busca
        self.search_thread = AutomatedSearchThread(
            search_queries=queries,
            save_dir=save_dir,
            file_format=file_format,
            headless_mode=headless_mode
        )

        # Conectar sinais
        self.search_thread.progress_updated.connect(self.progress_bar.setValue)
        self.search_thread.status_updated.connect(self.update_status_and_log)
        self.search_thread.finished_signal.connect(self.search_finished)
        self.search_thread.continue_query.connect(self.show_continue_dialog)
        self.search_thread.query_completed.connect(self.query_completed)

        # Iniciar thread
        self.search_thread.start()
        self.status_label.setText("Iniciando buscas automatizadas...")
        self.add_log("Thread de busca iniciada")

    def pause_automated_search(self):
        """Pausa ou retoma a busca automatizada."""
        if not self.search_thread:
            return

        if self.search_thread.paused:
            # Retomar
            self.search_thread.resume()
            self.pause_button.setText("Pausar")
            self.pause_button.setIcon(QIcon.fromTheme("media-playback-pause"))
            self.status_label.setText("Buscas automatizadas retomadas")
            self.add_log("Busca retomada pelo usuário")
        else:
            # Pausar
            self.search_thread.pause()
            self.pause_button.setText("Continuar")
            self.pause_button.setIcon(QIcon.fromTheme("media-playback-start"))
            self.status_label.setText("Buscas automatizadas pausadas")
            self.add_log("Busca pausada pelo usuário")

    def stop_automated_search(self):
        """Para a busca automatizada."""
        if not self.search_thread:
            return

        reply = QMessageBox.question(
            self,
            "Confirmar",
            "Tem certeza que deseja interromper as buscas automatizadas?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.add_log("Parando busca automatizada...")
            self.search_thread.stop()
            self.search_thread = None

            # Atualizar UI
            self.start_button.setEnabled(True)
            self.pause_button.setEnabled(False)
            self.stop_button.setEnabled(False)
            self.pause_button.setText("Pausar")
            self.pause_button.setIcon(QIcon.fromTheme("media-playback-pause"))
            self.status_label.setText("Buscas automatizadas interrompidas")
            self.add_log("Busca interrompida pelo usuário")

    def search_finished(self, result):
        """Callback chamado quando a busca automatizada é finalizada."""
        self.start_button.setEnabled(True)
        self.pause_button.setEnabled(False)
        self.stop_button.setEnabled(False)

        if "ERRO" not in result:
            self.add_log(f"Busca finalizada com sucesso: {result}")
            QMessageBox.information(self, "Sucesso", result)
        else:
            self.add_log(f"Erro na busca: {result}")
            QMessageBox.warning(self, "Erro", result)

    def query_completed(self, index):
        """Callback chamado quando uma consulta é concluída."""
        # Adicionar log da consulta concluída
        self.config = config_manager.load_config()
        queries = self.config.get("search_queries", [])

        if index < len(queries):
            query_name = queries[index]
            self.add_log(f"Consulta {index + 1} concluída: {query_name}")

    def show_continue_dialog(self, message, require_response):
        """Exibe um diálogo perguntando se deseja continuar a busca."""
        if require_response:
            reply = QMessageBox.question(
                self,
                "Continuar Busca",
                message,
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                self.search_thread.continue_response = "S"
            else:
                self.search_thread.continue_response = "N"
