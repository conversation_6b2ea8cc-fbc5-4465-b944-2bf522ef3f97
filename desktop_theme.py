"""
Definições de tema e estilos para a aplicação desktop PROSPECTO.
Design inspirado na Meta com cores modernas e elegantes.
"""
from PyQt6.QtWidgets import (QWidget, QPushButton, QLineEdit, QLabel, QFrame,
                            QTabWidget, QGroupBox, QTextEdit, QComboBox)
from PyQt6.QtGui import QColor, QFont, QPalette, QIcon, QPixmap
from PyQt6.QtCore import Qt, QSize
import os

# Esquema de cores padronizado - Tons de Azul
# Cores principais
PRIMARY_COLOR = "#0070F3"  # Azul Elétrico - Botões principais, links, indicadores ativos
SECONDARY_COLOR = "#1E88E5"  # Azul Médio - Destaques secundários, ícones, hover states
ACCENT_COLOR = "#2196F3"  # Azul <PERSON>laro - Call-to-action, notifica<PERSON><PERSON><PERSON>, badges
BACKGROUND_COLOR = "#F5F5F5"  # Cinza Suave - Fundos, cards, áreas de descanso visual
CARD_COLOR = "#FFFFFF"  # Branco para cards e containers

# Cores de texto
TEXT_PRIMARY = "#0A0F24"  # Azul-Marinho - Texto principal, headers, fundo de rodapé
TEXT_SECONDARY = "#6B7280"  # Cinza médio para texto secundário
TEXT_HINT = "#9CA3AF"  # Cinza claro para dicas e placeholders

# Cores de estado usando tons de azul
SUCCESS_COLOR = "#1976D2"  # Azul escuro para sucesso
ERROR_COLOR = "#D32F2F"  # Vermelho para erro (mantido para clareza)
WARNING_COLOR = "#FF9800"  # Laranja para avisos (mantido para clareza)
INFO_COLOR = PRIMARY_COLOR  # Azul Elétrico para informações

# Cores de botões
BUTTON_PRIMARY = PRIMARY_COLOR
BUTTON_SECONDARY = SECONDARY_COLOR
BUTTON_DISABLED = "#D1D5DB"  # Cinza claro para botões desabilitados

# Gradiente em tons de azul
GRADIENT_START = PRIMARY_COLOR  # #0070F3
GRADIENT_END = SECONDARY_COLOR  # #1E88E5

# Estilos de texto (Meta usa fontes modernas)
FONT_FAMILY = "Segoe UI, system-ui, -apple-system, BlinkMacSystemFont"
HEADING_SIZE = 24
SUBHEADING_SIZE = 18
BODY_SIZE = 14
CAPTION_SIZE = 12

# Espaçamento
PADDING_SMALL = 8
PADDING_MEDIUM = 16
PADDING_LARGE = 24
PADDING_XLARGE = 32

# Bordas
BORDER_RADIUS_SMALL = 4
BORDER_RADIUS_MEDIUM = 8
BORDER_RADIUS_LARGE = 12

def get_application_palette():
    """Retorna a paleta de cores para a aplicação."""
    palette = QPalette()

    # Cores de fundo
    palette.setColor(QPalette.ColorRole.Window, QColor(BACKGROUND_COLOR))
    palette.setColor(QPalette.ColorRole.Base, QColor(CARD_COLOR))

    # Cores de texto
    palette.setColor(QPalette.ColorRole.WindowText, QColor(TEXT_PRIMARY))
    palette.setColor(QPalette.ColorRole.Text, QColor(TEXT_PRIMARY))
    palette.setColor(QPalette.ColorRole.PlaceholderText, QColor(TEXT_HINT))

    # Cores de botões
    palette.setColor(QPalette.ColorRole.Button, QColor(BUTTON_PRIMARY))
    palette.setColor(QPalette.ColorRole.ButtonText, QColor("white"))

    # Cores de destaque
    palette.setColor(QPalette.ColorRole.Highlight, QColor(PRIMARY_COLOR))
    palette.setColor(QPalette.ColorRole.HighlightedText, QColor("white"))

    return palette

def get_application_stylesheet():
    """Retorna o stylesheet para a aplicação com design Meta-inspired."""
    return f"""
        /* Estilo geral Meta-inspired */
        QWidget {{
            font-family: {FONT_FAMILY};
            font-size: {BODY_SIZE}px;
            color: {TEXT_PRIMARY};
            background-color: {BACKGROUND_COLOR};
        }}

        /* Botões estilo Meta */
        QPushButton {{
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 {PRIMARY_COLOR}, stop: 1 #0056CC);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-weight: 600;
            font-size: 14px;
            min-height: 40px;
            text-transform: none;
        }}

        QPushButton:hover {{
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 #0056CC, stop: 1 #003D99);
        }}

        QPushButton:pressed {{
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 #003D99, stop: 1 #002966);
        }}

        QPushButton:disabled {{
            background-color: {BUTTON_DISABLED};
            color: {TEXT_HINT};
        }}

        QPushButton.secondary {{
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 {SECONDARY_COLOR}, stop: 1 #1565C0);
            color: white;
        }}

        QPushButton.secondary:hover {{
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 #1565C0, stop: 1 #0D47A1);
        }}

        QPushButton.accent {{
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 {ACCENT_COLOR}, stop: 1 #1976D2);
            color: white;
        }}

        QPushButton.accent:hover {{
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 #1976D2, stop: 1 #1565C0);
        }}

        QPushButton.success {{
            background-color: {SUCCESS_COLOR};
        }}

        QPushButton.error {{
            background-color: {ERROR_COLOR};
        }}

        QPushButton.warning {{
            background-color: {WARNING_COLOR};
            color: {TEXT_PRIMARY};
        }}

        QPushButton.info {{
            background-color: {INFO_COLOR};
        }}

        /* Campos de texto estilo Meta */
        QLineEdit, QTextEdit, QComboBox {{
            border: 2px solid #E5E7EB;
            border-radius: 12px;
            padding: 12px 16px;
            background-color: {CARD_COLOR};
            selection-background-color: {PRIMARY_COLOR};
            selection-color: white;
            font-size: 14px;
            color: {TEXT_PRIMARY};
        }}

        QLineEdit:focus, QTextEdit:focus, QComboBox:focus {{
            border: 2px solid {PRIMARY_COLOR};
            outline: none;
        }}

        QLineEdit::placeholder, QTextEdit::placeholder {{
            color: {TEXT_HINT};
        }}

        /* Abas estilo Meta */
        QTabWidget::pane {{
            border: none;
            border-radius: 16px;
            background-color: {CARD_COLOR};
            margin-top: 8px;
        }}

        QTabBar::tab {{
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 #F9FAFB, stop: 1 #F3F4F6);
            color: {TEXT_SECONDARY};
            border-top-left-radius: 12px;
            border-top-right-radius: 12px;
            padding: 12px 24px;
            margin-right: 4px;
            font-weight: 500;
            min-width: 100px;
        }}

        QTabBar::tab:selected {{
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 {PRIMARY_COLOR}, stop: 1 #0056CC);
            color: white;
            font-weight: 600;
        }}

        QTabBar::tab:hover:!selected {{
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 {SECONDARY_COLOR}, stop: 1 #2DD4AA);
            color: {TEXT_PRIMARY};
        }}

        /* Grupos estilo Meta */
        QGroupBox {{
            border: 2px solid #E5E7EB;
            border-radius: 16px;
            margin-top: 1.5ex;
            background-color: {CARD_COLOR};
            font-weight: 600;
        }}

        QGroupBox::title {{
            subcontrol-origin: margin;
            subcontrol-position: top left;
            padding: 0 12px;
            color: {TEXT_PRIMARY};
            font-weight: 600;
            font-size: 16px;
        }}

        /* Frames estilo Meta */
        QFrame.card {{
            border: none;
            border-radius: 16px;
            background-color: {CARD_COLOR};
        }}

        QFrame#settingsFrame {{
            border: 2px solid #E5E7EB;
            border-radius: 16px;
            background-color: {CARD_COLOR};
            padding: 16px;
        }}

        /* Barras de progresso estilo Meta */
        QProgressBar {{
            border: none;
            border-radius: 8px;
            background-color: #E5E7EB;
            text-align: center;
            height: 8px;
            font-weight: 600;
            color: {TEXT_PRIMARY};
        }}

        QProgressBar::chunk {{
            background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                      stop: 0 {PRIMARY_COLOR}, stop: 1 {SECONDARY_COLOR});
            border-radius: 8px;
        }}

        /* Cabeçalhos de tabelas estilo Meta */
        QHeaderView::section {{
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 {PRIMARY_COLOR}, stop: 1 #0056CC);
            color: white;
            padding: 12px;
            border: none;
            font-weight: 600;
            font-size: 14px;
        }}

        /* Tabelas estilo Meta */
        QTableWidget {{
            gridline-color: #E5E7EB;
            selection-background-color: {PRIMARY_COLOR};
            selection-color: white;
            border: none;
            border-radius: 12px;
            background-color: {CARD_COLOR};
        }}

        QTableWidget::item {{
            padding: 8px;
            border-bottom: 1px solid #F3F4F6;
        }}

        QTableWidget::item:selected {{
            background-color: {PRIMARY_COLOR};
            color: white;
        }}

        /* Scrollbars estilo Meta */
        QScrollBar:vertical {{
            border: none;
            background: transparent;
            width: 12px;
            margin: 0px;
        }}

        QScrollBar::handle:vertical {{
            background: #D1D5DB;
            min-height: 20px;
            border-radius: 6px;
            margin: 2px;
        }}

        QScrollBar::handle:vertical:hover {{
            background: #9CA3AF;
        }}

        QScrollBar:horizontal {{
            border: none;
            background: transparent;
            height: 12px;
            margin: 0px;
        }}

        QScrollBar::handle:horizontal {{
            background: #D1D5DB;
            min-width: 20px;
            border-radius: 6px;
            margin: 2px;
        }}

        QScrollBar::handle:horizontal:hover {{
            background: #9CA3AF;
        }}

        QScrollBar::add-line, QScrollBar::sub-line {{
            border: none;
            background: none;
        }}

        /* TreeWidget estilo Meta */
        QTreeWidget {{
            border: none;
            border-radius: 12px;
            background-color: {CARD_COLOR};
            selection-background-color: {PRIMARY_COLOR};
            selection-color: white;
            outline: none;
        }}

        QTreeWidget::item {{
            padding: 8px;
            border-bottom: 1px solid #F3F4F6;
            color: {TEXT_PRIMARY};
        }}

        QTreeWidget::item:selected {{
            background-color: {PRIMARY_COLOR};
            color: white;
            border-radius: 6px;
        }}

        QTreeWidget::item:hover {{
            background-color: #F3F4F6;
            border-radius: 6px;
        }}

        QTreeWidget::branch {{
            background: transparent;
        }}

        /* Labels especiais */
        QLabel#statusLabel {{
            color: {TEXT_SECONDARY};
            font-weight: 500;
            padding: 8px;
        }}
    """

def style_heading_label(label, size=HEADING_SIZE):
    """Estiliza um QLabel como título."""
    font = QFont(FONT_FAMILY, size)
    font.setBold(True)
    label.setFont(font)
    label.setStyleSheet(f"color: {PRIMARY_COLOR};")
    return label

def style_subheading_label(label, size=SUBHEADING_SIZE):
    """Estiliza um QLabel como subtítulo."""
    font = QFont(FONT_FAMILY, size)
    font.setBold(True)
    label.setFont(font)
    label.setStyleSheet(f"color: {TEXT_PRIMARY};")
    return label

def style_body_label(label, size=BODY_SIZE):
    """Estiliza um QLabel como texto normal."""
    font = QFont(FONT_FAMILY, size)
    label.setFont(font)
    label.setStyleSheet(f"color: {TEXT_PRIMARY};")
    return label

def style_caption_label(label, size=CAPTION_SIZE):
    """Estiliza um QLabel como legenda."""
    font = QFont(FONT_FAMILY, size)
    label.setFont(font)
    label.setStyleSheet(f"color: {TEXT_SECONDARY};")
    return label

def create_card_frame(parent=None):
    """Cria um QFrame estilizado como card."""
    frame = QFrame(parent)
    frame.setFrameShape(QFrame.Shape.StyledPanel)
    frame.setFrameShadow(QFrame.Shadow.Raised)
    frame.setStyleSheet(f"""
        QFrame {{
            background-color: {CARD_COLOR};
            border-radius: {BORDER_RADIUS_MEDIUM}px;
            border: 1px solid #BDBDBD;
        }}
    """)
    return frame

def style_primary_button(button):
    """Estiliza um QPushButton como botão primário com tons de azul."""
    button.setStyleSheet(f"""
        QPushButton {{
            background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                      stop: 0 {PRIMARY_COLOR}, stop: 1 {SECONDARY_COLOR});
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-weight: 600;
            font-size: 14px;
            min-height: 40px;
        }}
        QPushButton:hover {{
            background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                      stop: 0 #0056CC, stop: 1 #1565C0);
        }}
        QPushButton:pressed {{
            background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                      stop: 0 #003D99, stop: 1 #0D47A1);
        }}
        QPushButton:disabled {{
            background-color: {BUTTON_DISABLED};
            color: {TEXT_HINT};
        }}
    """)
    return button

def style_secondary_button(button):
    """Estiliza um QPushButton como botão secundário com tons de azul."""
    button.setStyleSheet(f"""
        QPushButton {{
            background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                      stop: 0 {SECONDARY_COLOR}, stop: 1 {ACCENT_COLOR});
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-weight: 600;
            font-size: 14px;
            min-height: 40px;
        }}
        QPushButton:hover {{
            background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                      stop: 0 #1565C0, stop: 1 #1976D2);
        }}
        QPushButton:pressed {{
            background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                      stop: 0 #0D47A1, stop: 1 #1565C0);
        }}
        QPushButton:disabled {{
            background-color: {BUTTON_DISABLED};
            color: {TEXT_HINT};
        }}
    """)
    return button

def style_accent_button(button):
    """Estiliza um QPushButton como botão de destaque com tons de azul."""
    button.setStyleSheet(f"""
        QPushButton {{
            background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                      stop: 0 {ACCENT_COLOR}, stop: 1 {PRIMARY_COLOR});
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-weight: 600;
            font-size: 14px;
            min-height: 40px;
        }}
        QPushButton:hover {{
            background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                      stop: 0 #1976D2, stop: 1 #0056CC);
        }}
        QPushButton:pressed {{
            background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                      stop: 0 #1565C0, stop: 1 #003D99);
        }}
        QPushButton:disabled {{
            background-color: {BUTTON_DISABLED};
            color: {TEXT_HINT};
        }}
    """)
    return button

def get_logo_pixmap(size=QSize(80, 80)):
    """Retorna um QPixmap com o logo do PROSPECTO."""
    logo_path = os.path.join("assets", "img", "logo.png")
    if os.path.exists(logo_path):
        return QPixmap(logo_path).scaled(size, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
    return None
